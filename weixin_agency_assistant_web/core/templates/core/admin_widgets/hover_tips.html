{% load static %}

<style>
.hover-tip-container {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.hover-tip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    max-width: 300px;
    {#white-space: normal;#}
    word-wrap: break-word;
}

.hover-tip::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #333;
}

.hover-tip-container:hover .hover-tip {
    opacity: 1;
    visibility: visible;
}

.restriction-status {
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

.restriction-none {
    color: #28a745;
    background-color: #d4edda;
}

.restriction-limited {
    color: #dc3545;
    background-color: #f8d7da;
}
</style>

<div class="hover-tip-container">
    {% if has_restriction %}
        <span class="restriction-status restriction-limited">✗ 有限制</span>
        <div class="hover-tip">
            {{ restriction_reason|default:"未知限制原因" }}
        </div>
    {% else %}
        <span class="restriction-status restriction-none">✓ 无限制</span>
        <div class="hover-tip">
            该商品可以正常推广，无任何限制
        </div>
    {% endif %}
</div>
