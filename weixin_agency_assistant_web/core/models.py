from django.core.serializers.json import DjangoJSONEncoder
from django.db import models
from django.db.models import F, DecimalField
from django.db.models.fields.json import KeyTextTransform
from django.db.models.functions import Cast


# Create your models here.

class Product(models.Model):
    class Meta:
        verbose_name = '商品'
        verbose_name_plural = '商品'

    product_data = models.JSONField(
        null=True,
        blank=True,
        default=dict,
        encoder=DjangoJSONEncoder,
    )

    product_title = models.GeneratedField(
        expression=KeyTextTransform(
            'title',
            KeyTextTransform('productInfo', 'product_data')
        ),
        output_field=models.CharField(max_length=1024),
        db_persist=True,
        verbose_name='商品名称',
        unique=True,
    )

    product_price = models.GeneratedField(
        expression=Cast(
            KeyTextTransform('price', KeyTextTransform('productInfo', 'product_data')),
            DecimalField(max_digits=9, decimal_places=2)
        ) / 100,
        output_field=DecimalField(max_digits=9, decimal_places=2),
        db_persist=True,
        verbose_name='商品价格'
    )

    # 商品图片URL
    product_img_url = models.GeneratedField(
        expression=KeyTextTransform(
            'imgUrl',
            KeyTextTransform('productInfo', 'product_data')
        ),
        output_field=models.URLField(max_length=1024),
        db_persist=True,
        verbose_name='商品图片'
    )

    # 库存数量
    stock_num = models.GeneratedField(
        expression=Cast(
            KeyTextTransform('stockNum', KeyTextTransform('productInfo', 'product_data')),
            models.IntegerField()
        ),
        output_field=models.IntegerField(),
        db_persist=True,
        verbose_name='库存数量'
    )

    # 佣金比例
    commission_ratio = models.GeneratedField(
        expression=Cast(
            KeyTextTransform('ratio', KeyTextTransform('commissionInfo', 'product_data')),
            DecimalField(max_digits=5, decimal_places=2)
        ),
        output_field=DecimalField(max_digits=5, decimal_places=2),
        db_persist=True,
        verbose_name='佣金比例(%)'
    )

    # 服务费比例
    service_ratio = models.GeneratedField(
        expression=Cast(
            KeyTextTransform('serviceRatio', KeyTextTransform('commissionInfo', 'product_data')),
            DecimalField(max_digits=5, decimal_places=2)
        ),
        output_field=DecimalField(max_digits=5, decimal_places=2),
        db_persist=True,
        verbose_name='服务费比例(%)'
    )

    # 佣金金额
    commission_amount = models.GeneratedField(
        expression=Cast(
            KeyTextTransform('amount', KeyTextTransform('commissionInfo', 'product_data')),
            DecimalField(max_digits=9, decimal_places=2)
        ) / 100,
        output_field=DecimalField(max_digits=9, decimal_places=2),
        db_persist=True,
        verbose_name='佣金金额'
    )

    # 服务费金额
    service_amount = models.GeneratedField(
        expression=Cast(
            KeyTextTransform('serviceAmount', KeyTextTransform('commissionInfo', 'product_data')),
            DecimalField(max_digits=9, decimal_places=2)
        ) / 100,
        output_field=DecimalField(max_digits=9, decimal_places=2),
        db_persist=True,
        verbose_name='服务费金额'
    )

    # 商品状态
    product_status = models.GeneratedField(
        expression=Cast(
            KeyTextTransform('status', 'product_data'),
            models.IntegerField()
        ),
        output_field=models.IntegerField(),
        db_persist=True,
        verbose_name='商品状态'
    )

    # 商品ID
    product_id = models.GeneratedField(
        expression=KeyTextTransform('id', 'product_data'),
        output_field=models.CharField(max_length=100),
        db_persist=True,
        verbose_name='商品ID'
    )

    # SPU ID
    spu_id = models.GeneratedField(
        expression=KeyTextTransform('spuId', 'product_data'),
        output_field=models.CharField(max_length=100),
        db_persist=True,
        verbose_name='SPU ID'
    )

    # 店铺名称
    shop_name = models.GeneratedField(
        expression=KeyTextTransform(
            'name',
            KeyTextTransform('shopInfo', 'product_data')
        ),
        output_field=models.CharField(max_length=255),
        db_persist=True,
        verbose_name='店铺名称'
    )

    # 店铺头像
    shop_head_img = models.GeneratedField(
        expression=KeyTextTransform(
            'headImg',
            KeyTextTransform('shopInfo', 'product_data')
        ),
        output_field=models.URLField(max_length=1024),
        db_persist=True,
        verbose_name='店铺头像'
    )

    # 推广开始时间
    promotion_start_time = models.GeneratedField(
        expression=Cast(
            KeyTextTransform('startTime', KeyTextTransform('commissionInfo', 'product_data')),
            models.BigIntegerField()
        ),
        output_field=models.BigIntegerField(),
        db_persist=True,
        verbose_name='推广开始时间'
    )

    # 推广结束时间
    promotion_end_time = models.GeneratedField(
        expression=Cast(
            KeyTextTransform('endTime', KeyTextTransform('commissionInfo', 'product_data')),
            models.BigIntegerField()
        ),
        output_field=models.BigIntegerField(),
        db_persist=True,
        verbose_name='推广结束时间'
    )

    # 来源名称
    source_name = models.GeneratedField(
        expression=KeyTextTransform(
            'sourceName',
            KeyTextTransform('itemInfo', 'product_data')
        ),
        output_field=models.CharField(max_length=255),
        db_persist=True,
        verbose_name='来源名称'
    )

    # 商品链接
    item_link = models.GeneratedField(
        expression=KeyTextTransform(
            'itemLink',
            KeyTextTransform('itemInfo', 'product_data')
        ),
        output_field=models.CharField(max_length=500),
        db_persist=True,
        verbose_name='商品链接'
    )

    # 佣金类型
    commission_type = models.GeneratedField(
        expression=Cast(
            KeyTextTransform('commissionType', KeyTextTransform('commissionInfo', 'product_data')),
            models.IntegerField()
        ),
        output_field=models.IntegerField(),
        db_persist=True,
        verbose_name='佣金类型'
    )

    # 二级服务费金额
    second_service_amount = models.GeneratedField(
        expression=Cast(
            KeyTextTransform('secondServiceAmount', KeyTextTransform('commissionInfo', 'product_data')),
            DecimalField(max_digits=9, decimal_places=2)
        ) / 100,
        output_field=DecimalField(max_digits=9, decimal_places=2),
        db_persist=True,
        verbose_name='二级服务费金额'
    )

    # 普通二级服务费金额
    normal_second_service_amount = models.GeneratedField(
        expression=Cast(
            KeyTextTransform('normalSecondServiceAmount', KeyTextTransform('commissionInfo', 'product_data')),
            DecimalField(max_digits=9, decimal_places=2)
        ) / 100,
        output_field=DecimalField(max_digits=9, decimal_places=2),
        db_persist=True,
        verbose_name='普通二级服务费金额'
    )

    def __str__(self):
        return self.product_title

    @property
    def status_display(self):
        """商品状态显示"""
        status_map = {
            1: '推广中',
            0: '已下架',
            2: '审核中'
        }
        return status_map.get(self.product_status, '未知')

    @property
    def promotion_time_display(self):
        """推广时间显示"""
        if self.promotion_end_time == 4294967290:  # 永久推广的特殊值
            return '永久'
        return f'{self.promotion_start_time} - {self.promotion_end_time}'

    @property
    def can_live_stream(self):
        """是否可以上架到直播间"""
        try:
            item_info = self.product_data.get('itemInfo', {})
            result = item_info.get('result', {})
            return result.get('admission', False)
        except:
            return False

    @property
    def live_stream_rejection_reason(self):
        """不可上架到直播间的原因"""
        try:
            item_info = self.product_data.get('itemInfo', {})
            result = item_info.get('result', {})
            rejection_list = result.get('list', [])
            if rejection_list:
                return rejection_list[0].get('wording', '未知原因')
            return None
        except:
            return None

    @property
    def guarantee_flags(self):
        """商品保障标识列表"""
        try:
            product_info = self.product_data.get('productInfo', {})
            guarantee_flags = product_info.get('guaranteeFlag', [])
            return [flag.get('guarantee', '') for flag in guarantee_flags]
        except:
            return []

    @property
    def guarantee_flags_display(self):
        """商品保障标识显示"""
        flags = self.guarantee_flags
        return ' | '.join(flags) if flags else '无保障'

    @property
    def commission_type_display(self):
        """佣金类型显示"""
        type_map = {
            0: '普通佣金',
            1: '阶梯佣金',
            2: '固定佣金'
        }
        return type_map.get(self.commission_type, f'类型{self.commission_type}')
