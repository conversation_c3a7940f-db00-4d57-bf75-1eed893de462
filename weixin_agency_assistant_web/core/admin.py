from django.contrib import admin
from django.utils.html import format_html
from django.utils.safestring import mark_safe

from core.models import Product


# Register your models here.

@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    model_fields = [
        'product_title',
        'product_price',
        'commission_ratio',
        'service_ratio',
        'commission_amount',
        'service_amount',
        'stock_num',
        'product_status',
        'shop_name',
        'product_id',
        'spu_id',
        'is_commission_hidden',
        'promotion_start_time',
        'promotion_end_time',
    ]

    list_display = [
        'product_image_display',
        'product_title',
        'product_price',
        'commission_display',
        'service_fee_display',
        'promotion_time_display_admin',
        'status_display_admin',
        'shop_name',
        'stock_num',
        'live_stream_status',
    ]

    list_filter = [
        'product_status',
        'is_commission_hidden',
        'shop_name',
    ]

    search_fields = [
        'product_title',
        'product_id',
        'spu_id',
        'shop_name',
    ]

    fields = ['product_data'] + model_fields
    readonly_fields = model_fields

    @admin.display(description='商品图片')
    def product_image_display(self, obj):
        """显示商品图片"""
        if obj.product_img_url:
            return format_html(
                '<img src="{}" width="50" height="50" style="object-fit: cover;" />',
                obj.product_img_url
            )
        return "无图片"

    @admin.display(description='达人佣金')
    def commission_display(self, obj):
        """显示佣金信息"""
        if obj.is_commission_hidden:
            return "隐藏"
        return f"{obj.commission_amount}元\n({obj.commission_ratio}%)"

    @admin.display(description='我的服务费')
    def service_fee_display(self, obj):
        """显示服务费信息"""
        if obj.is_commission_hidden:
            return "隐藏"
        return f"{obj.service_amount}元\n({obj.service_ratio}%)"

    @admin.display(description='推广时间')
    def promotion_time_display_admin(self, obj):
        """显示推广时间"""
        return obj.promotion_time_display

    @admin.display(description='状态')
    def status_display_admin(self, obj):
        """显示商品状态"""
        status = obj.status_display
        if status == '推广中':
            color = 'green'
        elif status == '已下架':
            color = 'red'
        else:
            color = 'orange'

        return format_html(
            '<span style="color: {};">{}</span>',
            color,
            status
        )

    @admin.display(description='直播间状态')
    def live_stream_status(self, obj):
        """显示是否可以上架到直播间"""
        if obj.can_live_stream:
            return format_html('<span style="color: green;">✓ 可上架</span>')
        else:
            return format_html('<span style="color: red;">✗ 不可上架</span>')

    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related()
