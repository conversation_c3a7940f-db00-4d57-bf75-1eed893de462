from django.contrib import admin
from django.utils.html import format_html
from django.utils.safestring import mark_safe

from core.models import Product


# Register your models here.

@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    model_fields = [
        'product_title',
        'product_price',
        'commission_ratio',
        'service_ratio',
        'commission_amount',
        'service_amount',
        'stock_num',
        'product_status',
        'shop_name',
        'product_id',
        'spu_id',
        'promotion_start_time',
        'promotion_end_time',
        'product_type',
        'source_name',
        'item_link',
        'commission_type',
        'second_service_amount',
        'normal_second_service_amount',
    ]

    list_display = [
        'product_image_display',
        'product_title',
        'product_price',
        'commission_display',
        'service_fee_display',
        'promotion_time_display_admin',
        'status_display_admin',
        'shop_name',
        'stock_num',
        'live_stream_status',
        'guarantee_display',
        'product_type_display_admin',
    ]

    list_filter = [
        'product_status',
        'shop_name',
        'product_type',
        'commission_type',
        'source_name',
    ]

    search_fields = [
        'product_title',
        'product_id',
        'spu_id',
        'shop_name',
    ]

    fields = ['product_data'] + model_fields
    readonly_fields = model_fields

    @admin.display(
        description='商品图片',
        empty_value='无图片'
    )
    def product_image_display(self, obj):
        """显示商品图片"""
        if obj.product_img_url:
            return format_html(
                '<img src="{}" width="50" height="50" style="object-fit: cover;" />',
                obj.product_img_url,
            )
        return None

    @admin.display(
        description='达人佣金',
        ordering='commission_amount'
    )
    def commission_display(self, obj):
        """显示佣金信息"""
        return f"{obj.commission_amount}元\n({obj.commission_ratio}%)"

    @admin.display(
        description='我的服务费',
        ordering='service_amount'
    )
    def service_fee_display(self, obj):
        """显示服务费信息"""
        return f"{obj.service_amount}元\n({obj.service_ratio}%)"

    @admin.display(
        description='推广时间',
        ordering='promotion_end_time'
    )
    def promotion_time_display_admin(self, obj):
        """显示推广时间"""
        return obj.promotion_time_display

    @admin.display(
        description='状态',
        ordering='product_status'
    )
    def status_display_admin(self, obj):
        """显示商品状态"""
        status = obj.status_display
        if status == '推广中':
            color = 'green'
        elif status == '已下架':
            color = 'red'
        else:
            color = 'orange'

        return format_html(
            '<span style="color: {};">{}</span>',
            color,
            status,
        )

    @admin.display(description='推广限制')
    def promotion_restriction_status(self, obj):
        """显示推广限制状态，鼠标悬停显示原因"""
        if obj.can_live_stream:
            return format_html(
                '<span style="color: green;" title="无限制">✓ 无限制</span>',
            )
        else:
            reason = obj.live_stream_rejection_reason or "未知限制原因"
            return format_html(
                '<span style="color: red;" title="{}">✗ 有限制</span>',
                reason,
            )

    @admin.display(
        description='商品保障',
        ordering='product_data'
    )
    def guarantee_display(self, obj):
        """显示商品保障标识"""
        return obj.guarantee_flags_display

    @admin.display(
        description='商品类型',
        ordering='product_type'
    )
    def product_type_display_admin(self, obj):
        """显示商品类型"""
        return obj.product_type_display

    @admin.display(
        description='佣金类型',
        ordering='commission_type'
    )
    def commission_type_display_admin(self, obj):
        """显示佣金类型"""
        return obj.commission_type_display

    @admin.display(
        description='不可上架原因'
    )
    def rejection_reason_display(self, obj):
        """显示不可上架到直播间的原因"""
        if not obj.can_live_stream:
            reason = obj.live_stream_rejection_reason
            if reason:
                return format_html(
                    '<span style="color: red; font-size: 12px;">{}</span>',
                    reason,
                )
        return '-'

    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related()
